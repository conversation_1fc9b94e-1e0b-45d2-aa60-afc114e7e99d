class VariantFiltering {
  constructor() {
    this.init();
  }

  init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupFiltering());
    } else {
      this.setupFiltering();
    }
  }

  setupFiltering() {
    // Only run if we have variant cards (show_variants_as_products is enabled)
    const variantCards = document.querySelectorAll('[data-variant-color]');
    if (variantCards.length === 0) return;

    // Listen for facet form changes
    this.observeFacetChanges();

    // Apply initial filtering based on current URL params
    this.applyInitialFiltering();
  }

  observeFacetChanges() {
    // Listen for facet form submissions
    const facetForms = document.querySelectorAll('facet-filters-form form');
    facetForms.forEach(form => {
      form.addEventListener('input', (event) => {
        // Small delay to let the form update
        setTimeout(() => this.handleFilterChange(), 100);
      });
    });

    // Also listen for mobile facet changes
    const mobileFacetForm = document.querySelector('#FacetFiltersFormMobile');
    if (mobileFacetForm) {
      mobileFacetForm.addEventListener('input', (event) => {
        setTimeout(() => this.handleFilterChange(), 100);
      });
    }

    // Listen for active filter removals
    document.addEventListener('click', (event) => {
      if (event.target.matches('.js-facet-remove, .js-facet-remove *')) {
        setTimeout(() => this.handleFilterChange(), 100);
      }
    });
  }

  applyInitialFiltering() {
    // Check URL params for active color filters
    const urlParams = new URLSearchParams(window.location.search);
    const colorFilters = this.getActiveColorFilters();

    if (colorFilters.length > 0) {
      this.filterVariantCards(colorFilters);
    }
  }

  handleFilterChange() {
    const colorFilters = this.getActiveColorFilters();
    this.filterVariantCards(colorFilters);
  }

  getActiveColorFilters() {
    const activeFilters = [];

    // Check all filter checkboxes that are checked and look for color-related ones
    const allFilterInputs = document.querySelectorAll('input[type="checkbox"][name*="filter"]');

    allFilterInputs.forEach(input => {
      if (input.checked) {
        const name = input.name.toLowerCase();
        const value = input.value;

        // Check if this is a color filter by looking at the name or value
        if (name.includes('color') || name.includes('colour')) {
          // Extract the color value - it might be just the color name or "color:ColorName"
          let colorValue = value;
          if (value.includes(':')) {
            colorValue = value.split(':').pop();
          }

          // Convert to handle format (lowercase, spaces to dashes)
          const handleValue = colorValue.toLowerCase().replace(/\s+/g, '-');
          activeFilters.push(handleValue);
        }
      }
    });

    // Debug logging
    console.log('Active color filters:', activeFilters);

    return activeFilters;
  }

  filterVariantCards(activeColorFilters) {
    const variantCards = document.querySelectorAll('[data-variant-color]');
    const regularCards = document.querySelectorAll('.card-wrapper:not([data-variant-color])');

    console.log('Filtering variant cards. Active filters:', activeColorFilters);
    console.log('Found variant cards:', variantCards.length);
    console.log('Found regular cards:', regularCards.length);

    if (activeColorFilters.length === 0) {
      // No color filters active - show all cards
      variantCards.forEach(card => {
        card.style.display = '';
        card.closest('li').style.display = '';
      });
      regularCards.forEach(card => {
        card.style.display = '';
        card.closest('li').style.display = '';
      });
      return;
    }

    // Hide regular product cards when color filtering is active
    regularCards.forEach(card => {
      card.style.display = 'none';
      card.closest('li').style.display = 'none';
    });

    // Show/hide variant cards based on color filter
    let visibleCount = 0;
    variantCards.forEach(card => {
      const cardColor = card.getAttribute('data-variant-color');
      const shouldShow = activeColorFilters.includes(cardColor);

      console.log(`Card color: "${cardColor}", Should show: ${shouldShow}`);

      card.style.display = shouldShow ? '' : 'none';
      card.closest('li').style.display = shouldShow ? '' : 'none';

      if (shouldShow) visibleCount++;
    });

    console.log(`Showing ${visibleCount} variant cards`);

    // Update product count if needed
    this.updateProductCount();
  }

  updateProductCount() {
    const visibleCards = document.querySelectorAll('.card-wrapper:not([style*="display: none"])');
    const countElements = document.querySelectorAll('#ProductCount .facets__selected, #ProductCountDesktop .facets__selected');

    countElements.forEach(element => {
      // Update the count text while preserving the original structure
      const countText = element.textContent;
      const newCount = visibleCards.length;

      // Replace the number in the text
      const updatedText = countText.replace(/\d+/, newCount);
      element.textContent = updatedText;
    });
  }
}

// Initialize when the script loads
new VariantFiltering();
