{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}

{% if section.settings.image_shape == 'blob' %}
  {{ 'mask-blobs.css' | asset_url | stylesheet_tag }}
{%- endif -%}

{%- unless section.settings.quick_add == 'none' -%}
  {{ 'quick-add.css' | asset_url | stylesheet_tag }}
{%- endunless -%}

{%- if section.settings.quick_add == 'standard' -%}
  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- if section.settings.quick_add == 'bulk' -%}
  <script src="{{ 'quick-add-bulk.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'quantity-popover.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'price-per-item.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'quick-order-list.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="section-{{ section.id }}-padding gradient color-{{ section.settings.color_scheme }}">
  {%- paginate collection.products by section.settings.products_per_page -%}
    {% comment %} Sort is the first tabbable element when filter type is vertical {% endcomment %}
    {%- if section.settings.enable_sorting and section.settings.filter_type == 'vertical' -%}
      <facet-filters-form class="facets facets-vertical-sort page-width small-hide">
        <form class="facets-vertical-form" id="FacetSortForm">
          <div class="facet-filters sorting caption">
            <div class="facet-filters__field">
              <h2 class="facet-filters__label caption-large text-body">
                <label for="SortBy">{{ 'products.facets.sort_by_label' | t }}</label>
              </h2>
              <div class="select">
                {%- assign sort_by = collection.sort_by | default: collection.default_sort_by -%}
                <select
                  name="sort_by"
                  class="facet-filters__sort select__select caption-large"
                  id="SortBy"
                  aria-describedby="a11y-refresh-page-message"
                >
                  {%- for option in collection.sort_options -%}
                    <option
                      value="{{ option.value | escape }}"
                      {% if option.value == sort_by %}
                        selected="selected"
                      {% endif %}
                    >
                      {{ option.name | escape }}
                    </option>
                  {%- endfor -%}
                </select>
                <span class="svg-wrapper">
                  {{- 'icon-caret.svg' | inline_asset_content -}}
                </span>
              </div>
            </div>
          </div>

          <div class="product-count-vertical light" role="status">
            <h2 class="product-count__text text-body">
              <span id="ProductCountDesktop">
                {%- if collection.results_count -%}
                  {{
                    'templates.search.results_with_count'
                    | t: terms: collection.terms, count: collection.results_count
                  }}
                {%- elsif collection.products_count == collection.all_products_count -%}
                  {{ 'products.facets.product_count_simple' | t: count: collection.products_count }}
                {%- else -%}
                  {{
                    'products.facets.product_count'
                    | t: product_count: collection.products_count, count: collection.all_products_count
                  }}
                {%- endif -%}
              </span>
            </h2>
            {%- render 'loading-spinner' -%}
          </div>
        </form>
      </facet-filters-form>
    {%- endif -%}

    <div class="{% if section.settings.filter_type == 'vertical' %} facets-vertical page-width{% endif %}">
      {{ 'component-facets.css' | asset_url | stylesheet_tag }}
      <script src="{{ 'facets.js' | asset_url }}" defer="defer"></script>
      {%- if settings.show_variants_as_products -%}
        <script src="{{ 'variant-filtering.js' | asset_url }}" defer="defer"></script>
      {%- endif -%}
      {%- if section.settings.enable_filtering or section.settings.enable_sorting -%}
        <aside
          aria-labelledby="verticalTitle"
          class="facets-wrapper{% unless section.settings.enable_filtering %} facets-wrapper--no-filters{% endunless %}{% if section.settings.filter_type != 'vertical' %} page-width{% endif %}"
          id="main-collection-filters"
          data-id="{{ section.id }}"
        >
          {% render 'facets',
            results: collection,
            enable_filtering: section.settings.enable_filtering,
            enable_sorting: section.settings.enable_sorting,
            filter_type: section.settings.filter_type,
            paginate: paginate
          %}
        </aside>
      {%- endif -%}

      <div
        class="product-grid-container{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
        id="ProductGridContainer"
        {% if settings.animations_reveal_on_scroll %}
          data-cascade
        {% endif %}
      >
        {%- if collection.products.size == 0 -%}
          <div class="collection collection--empty page-width" id="product-grid" data-id="{{ section.id }}">
            <div class="loading-overlay gradient"></div>
            <div class="title-wrapper center">
              <h2 class="title title--primary">
                {{ 'sections.collection_template.empty' | t -}}
                <br>
                {{
                  'sections.collection_template.use_fewer_filters_html'
                  | t: link: collection.url, class: 'underlined-link link'
                }}
              </h2>
            </div>
          </div>
        {%- else -%}
          <div
            class="collection{% if section.settings.filter_type != 'vertical' %} page-width{% endif %}"
          >
            <div class="loading-overlay gradient"></div>
            <ul
              id="product-grid"
              data-id="{{ section.id }}"
              class="
                grid product-grid grid--{{ section.settings.columns_mobile }}-col-tablet-down
                grid--{{ section.settings.columns_desktop }}-col-desktop
                {% if section.settings.quick_add == 'bulk' %} collection-quick-add-bulk{% endif %}
              "
            >
              {% assign skip_card_product_styles = false %}
              {% assign card_index = 0 %}

              {%- if settings.show_variants_as_products -%}
                {%- comment -%} Show each color variant as a separate card {%- endcomment -%}
                {%- for product in collection.products -%}
                  {%- liquid
                    assign color_option = product.options_by_name.Color | default: product.options_by_name.color | default: product.options_by_name.Colour | default: product.options_by_name.colour
                    assign unique_colors = ''
                    assign color_variants = ''
                  -%}

                  {%- if color_option -%}
                    {%- comment -%} Product has color options - only show variants that have their own images {%- endcomment -%}
                    {%- assign color_index = color_option.position | minus: 1 -%}
                    {%- assign colors_with_images = '' -%}

                    {%- comment -%} First pass: find colors that have variant images {%- endcomment -%}
                    {%- for variant in product.variants -%}
                      {%- if variant.featured_image -%}
                        {%- assign variant_color = variant.options[color_index] -%}
                        {%- unless colors_with_images contains variant_color -%}
                          {%- assign colors_with_images = colors_with_images | append: variant_color | append: ',' -%}
                        {%- endunless -%}
                      {%- endif -%}
                    {%- endfor -%}

                    {%- if colors_with_images != '' -%}
                      {%- comment -%} Some colors have variant images - show only those as separate cards {%- endcomment -%}
                      {%- for variant in product.variants -%}
                        {%- assign variant_color = variant.options[color_index] -%}
                        {%- if variant.featured_image -%}
                          {%- unless unique_colors contains variant_color -%}
                            {%- assign unique_colors = unique_colors | append: variant_color | append: ',' -%}

                          {% assign card_index = card_index | plus: 1 %}
                          {% assign lazy_load = false %}
                          {%- if card_index > 2 -%}
                            {%- assign lazy_load = true -%}
                          {%- endif -%}

                          {%- liquid
                            assign variant_url = variant.url | default: product.url
                            assign variant_image = variant.featured_image
                          -%}

                          <li
                            class="grid__item{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                            {% if settings.animations_reveal_on_scroll %}
                              data-cascade
                              style="--animation-order: {{ card_index }};"
                            {% endif %}
                          >
                            {% render 'card-product',
                              card_product: product,
                              card_variant: variant,
                              custom_url: variant_url,
                              custom_image: variant_image,
                              media_aspect_ratio: section.settings.image_ratio,
                              image_shape: section.settings.image_shape,
                              show_secondary_image: section.settings.show_secondary_image,
                              show_vendor: section.settings.show_vendor,
                              show_rating: section.settings.show_rating,
                              lazy_load: lazy_load,
                              skip_styles: skip_card_product_styles,
                              quick_add: section.settings.quick_add,
                              section_id: section.id
                            %}
                          </li>
                          {%- assign skip_card_product_styles = true -%}
                          {%- endunless -%}
                        {%- endif -%}
                      {%- endfor -%}
                    {%- else -%}
                      {%- comment -%} Product has color options but no variant images - show as single product {%- endcomment -%}
                      {% assign card_index = card_index | plus: 1 %}
                      {% assign lazy_load = false %}
                      {%- if card_index > 2 -%}
                        {%- assign lazy_load = true -%}
                      {%- endif -%}
                      <li
                        class="grid__item{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                        {% if settings.animations_reveal_on_scroll %}
                          data-cascade
                          style="--animation-order: {{ card_index }};"
                        {% endif %}
                      >
                        {% render 'card-product',
                          card_product: product,
                          media_aspect_ratio: section.settings.image_ratio,
                          image_shape: section.settings.image_shape,
                          show_secondary_image: section.settings.show_secondary_image,
                          show_vendor: section.settings.show_vendor,
                          show_rating: section.settings.show_rating,
                          lazy_load: lazy_load,
                          skip_styles: skip_card_product_styles,
                          quick_add: section.settings.quick_add,
                          section_id: section.id
                        %}
                      </li>
                      {%- assign skip_card_product_styles = true -%}
                    {%- endif -%}
                  {%- else -%}
                    {%- comment -%} Product has no color options - show as single product {%- endcomment -%}
                    {% assign card_index = card_index | plus: 1 %}
                    {% assign lazy_load = false %}
                    {%- if card_index > 2 -%}
                      {%- assign lazy_load = true -%}
                    {%- endif -%}
                    <li
                      class="grid__item{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                      {% if settings.animations_reveal_on_scroll %}
                        data-cascade
                        style="--animation-order: {{ card_index }};"
                      {% endif %}
                    >
                      {% render 'card-product',
                        card_product: product,
                        media_aspect_ratio: section.settings.image_ratio,
                        image_shape: section.settings.image_shape,
                        show_secondary_image: section.settings.show_secondary_image,
                        show_vendor: section.settings.show_vendor,
                        show_rating: section.settings.show_rating,
                        lazy_load: lazy_load,
                        skip_styles: skip_card_product_styles,
                        quick_add: section.settings.quick_add,
                        section_id: section.id
                      %}
                    </li>
                    {%- assign skip_card_product_styles = true -%}
                  {%- endif -%}
                {%- endfor -%}
              {%- else -%}
                {%- comment -%} Traditional product display {%- endcomment -%}
                {%- for product in collection.products -%}
                  {% assign card_index = card_index | plus: 1 %}
                  {% assign lazy_load = false %}
                  {%- if card_index > 2 -%}
                    {%- assign lazy_load = true -%}
                  {%- endif -%}
                  <li
                    class="grid__item{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                    {% if settings.animations_reveal_on_scroll %}
                      data-cascade
                      style="--animation-order: {{ card_index }};"
                    {% endif %}
                  >
                    {% render 'card-product',
                      card_product: product,
                      media_aspect_ratio: section.settings.image_ratio,
                      image_shape: section.settings.image_shape,
                      show_secondary_image: section.settings.show_secondary_image,
                      show_vendor: section.settings.show_vendor,
                      show_rating: section.settings.show_rating,
                      lazy_load: lazy_load,
                      skip_styles: skip_card_product_styles,
                      quick_add: section.settings.quick_add,
                      section_id: section.id
                    %}
                  </li>
                  {%- assign skip_card_product_styles = true -%}
                {%- endfor -%}
              {%- endif -%}
            </ul>

            {%- if paginate.pages > 1 -%}
              {% render 'pagination', paginate: paginate, anchor: '' %}
            {%- endif -%}
          </div>
        {%- endif -%}
      </div>
    </div>
  {%- endpaginate -%}
  {% if section.settings.image_shape == 'arch' %}
    {{ 'mask-arch.svg' | inline_asset_content }}
  {%- endif -%}
</div>

{% schema %}
{
  "name": "t:sections.main-collection-product-grid.name",
  "class": "section",
  "settings": [
    {
      "type": "range",
      "id": "products_per_page",
      "min": 8,
      "max": 36,
      "step": 4,
      "default": 16,
      "label": "t:sections.main-collection-product-grid.settings.products_per_page.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 4,
      "label": "t:sections.main-collection-product-grid.settings.columns_desktop.label"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "2",
      "label": "t:sections.main-collection-product-grid.settings.columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.main-collection-product-grid.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.main-collection-product-grid.settings.columns_mobile.options__2.label"
        }
      ]
    },    
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.all.colors.has_cards_info",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.main-collection-product-grid.settings.header__3.content"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.main-collection-product-grid.settings.image_ratio.label"
    },
    {
      "type": "select",
      "id": "image_shape",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.image_shape.options__1.label"
        },
        {
          "value": "arch",
          "label": "t:sections.all.image_shape.options__2.label"
        },
        {
          "value": "blob",
          "label": "t:sections.all.image_shape.options__3.label"
        },
        {
          "value": "chevronleft",
          "label": "t:sections.all.image_shape.options__4.label"
        },
        {
          "value": "chevronright",
          "label": "t:sections.all.image_shape.options__5.label"
        },
        {
          "value": "diamond",
          "label": "t:sections.all.image_shape.options__6.label"
        },
        {
          "value": "parallelogram",
          "label": "t:sections.all.image_shape.options__7.label"
        },
        {
          "value": "round",
          "label": "t:sections.all.image_shape.options__8.label"
        }
      ],
      "default": "default",
      "label": "t:sections.all.image_shape.label"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "t:sections.main-collection-product-grid.settings.show_secondary_image.label"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "t:sections.main-collection-product-grid.settings.show_vendor.label"
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "default": false,
      "label": "t:sections.main-collection-product-grid.settings.show_rating.label",
      "info": "t:sections.main-collection-product-grid.settings.show_rating.info"
    },
    {
      "type": "select",
      "id": "quick_add",
      "default": "none",
      "label": "t:sections.main-collection-product-grid.settings.quick_add.label",
      "options": [
        {
          "value": "none",
          "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_1"
        },
        {
          "value": "standard",
          "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_2"
        },
        {
          "value": "bulk",
          "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_3"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.main-collection-product-grid.settings.header__1.content"
    },
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_filtering.label",
      "info": "t:sections.main-collection-product-grid.settings.enable_filtering.info"
    },
    {
      "type": "select",
      "id": "filter_type",
      "options": [
        {
          "value": "horizontal",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__1.label"
        },
        {
          "value": "vertical",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__2.label"
        },
        {
          "value": "drawer",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__3.label"
        }
      ],
      "default": "horizontal",
      "label": "t:sections.main-collection-product-grid.settings.filter_type.label"
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_sorting.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}
